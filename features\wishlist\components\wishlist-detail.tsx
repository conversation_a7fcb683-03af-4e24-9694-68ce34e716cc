import { useState } from "react";
import { Plus, ExternalLink, Edit, Trash2, ShoppingBag, Star, Calendar, Target } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { formatCurrency } from "@/lib/utils";
import { WishlistPossibilityForm } from "./wishlist-possibility-form";
import { WishlistSourceForm } from "./wishlist-source-form";
import { MediaGallery } from "@/components/media-gallery";
import { useCreateWishlistPossibility } from "../api/use-create-wishlist-possibility";
import { useEditWishlistPossibility } from "../api/use-edit-wishlist-possibility";
import { useDeleteWishlistPossibility } from "../api/use-delete-wishlist-possibility";
import { useCreateWishlistSource } from "../api/use-create-wishlist-source";
import { useEditWishlistSource } from "../api/use-edit-wishlist-source";
import { useDeleteWishlistSource } from "../api/use-delete-wishlist-source";
import { WishlistPossibilitiesTable } from "./wishlist-possibilities-table";
import { format } from "date-fns";

type WishlistSource = {
  id: string;
  name: string;
  url?: string | null;
  price?: number | null;
  notes?: string | null;
  possibilityId: string;
};

type WishlistPossibility = {
  id: string;
  name: string;
  description?: string | null;
  notes?: string | null;
  wishlistItemId: string;
  sources: WishlistSource[];
  createdAt: Date;
  updatedAt: Date;
};

type WishlistItem = {
  id: string;
  name: string;
  category: string;
  estimatedCost: number;
  targetAmount?: number;
  quantity: number;
  targetDate?: Date;
  priority: string;
  status: string;
  notes?: string;
  links?: string;
  imageUrl?: string;
  motivation?: string;
  possibilities: WishlistPossibility[];
};

type Props = {
  item: WishlistItem;
};

export const WishlistDetail = ({ item }: Props) => {
  const [possibilitySheetOpen, setPossibilitySheetOpen] = useState(false);
  const [sourceSheetOpen, setSourceSheetOpen] = useState(false);
  const [editingPossibility, setEditingPossibility] = useState<WishlistPossibility | null>(null);
  const [editingSource, setEditingSource] = useState<{ source: WishlistSource; possibilityId: string } | null>(null);
  const [addingSourceToPossibilityId, setAddingSourceToPossibilityId] = useState<string | null>(null);

  const createPossibilityMutation = useCreateWishlistPossibility();
  const editPossibilityMutation = useEditWishlistPossibility();
  const deletePossibilityMutation = useDeleteWishlistPossibility();
  const createSourceMutation = useCreateWishlistSource();
  const editSourceMutation = useEditWishlistSource();
  const deleteSourceMutation = useDeleteWishlistSource();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned": return "bg-blue-100 text-blue-800";
      case "in_progress": return "bg-purple-100 text-purple-800";
      case "on_hold": return "bg-orange-100 text-orange-800";
      case "achieved": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const handleCreatePossibility = (values: any, files?: FileList) => {
    createPossibilityMutation.mutate(
      { wishlistItemId: item.id, possibility: values, files },
      {
        onSuccess: () => {
          setPossibilitySheetOpen(false);
        },
      }
    );
  };

  const handleEditPossibility = (values: any, files?: FileList) => {
    if (!editingPossibility) return;
    editPossibilityMutation.mutate(
      { id: editingPossibility.id, possibility: values, files },
      {
        onSuccess: () => {
          setEditingPossibility(null);
          setPossibilitySheetOpen(false);
        },
      }
    );
  };

  const handleDeletePossibility = () => {
    if (!editingPossibility) return;
    deletePossibilityMutation.mutate(editingPossibility.id, {
      onSuccess: () => {
        setEditingPossibility(null);
        setPossibilitySheetOpen(false);
      },
    });
  };

  const handleCreateSource = (values: any) => {
    const possibilityId = addingSourceToPossibilityId;
    if (!possibilityId) return;
    createSourceMutation.mutate(
      { possibilityId, source: values },
      {
        onSuccess: () => {
          setSourceSheetOpen(false);
          setAddingSourceToPossibilityId(null);
        },
      }
    );
  };

  const handleEditSource = (values: any) => {
    if (!editingSource) return;
    editSourceMutation.mutate(
      { id: editingSource.source.id, source: values },
      {
        onSuccess: () => {
          setEditingSource(null);
          setSourceSheetOpen(false);
        },
      }
    );
  };

  const handleDeleteSource = () => {
    if (!editingSource) return;
    deleteSourceMutation.mutate(editingSource.source.id, {
      onSuccess: () => {
        setEditingSource(null);
        setSourceSheetOpen(false);
      },
    });
  };

  return (
    <div className="space-y-8">
      {/* Combined Header */}
      <Card className="border">
        <CardHeader className="gap-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
            {/* Left: Item summary */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-12 h-12 rounded-xl bg-indigo-600/10 text-indigo-700 flex items-center justify-center">
                <ShoppingBag className="h-6 w-6" />
              </div>
              <div className="min-w-0">
                <h1 className="text-2xl font-semibold text-gray-900">{item.name}</h1>
                <p className="text-sm text-indigo-700/90">{item.category}</p>
                <div className="mt-3 flex flex-wrap items-center gap-2">
                  <Badge className={`${getPriorityColor(item.priority)} px-2 py-1 text-xs font-medium`}>
                    <Star className="h-3 w-3 mr-1" />
                    {item.priority.toUpperCase()} PRIORITY
                  </Badge>
                  <Badge className={`${getStatusColor(item.status)} px-2 py-1 text-xs font-medium`}>
                    {item.status.replace("_", " ").toUpperCase()}
                  </Badge>
                  <Badge className="bg-gray-100 text-gray-800 px-2 py-1 text-xs font-medium">
                    Qty: {item.quantity}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Right: Actions */}
            <div className="flex flex-col items-stretch gap-2 sm:flex-row sm:items-center">
              {/* Add New Possibility Button */}
              <Button 
                onClick={() => {
                  setEditingPossibility(null);
                  setPossibilitySheetOpen(true);
                }}
                className=""
              >
                <Plus className="h-4 w-4 mr-1.5" />
                Add New Option
              </Button>
            </div>
          </div>

          {/* Quick stats row */}
          <div className="mt-2 flex flex-wrap items-center gap-3 text-sm text-gray-600">
            <span className="rounded-md bg-gray-50 px-2 py-1 font-medium text-gray-800">
              {formatCurrency(item.estimatedCost)}
            </span>
            {item.targetAmount && (
              <span className="rounded-md bg-gray-50 px-2 py-1">Target: {formatCurrency(item.targetAmount)}</span>
            )}
            {item.targetDate && (
              <span className="rounded-md bg-gray-50 px-2 py-1">Due: {format(new Date(item.targetDate), "MMM dd")}</span>
            )}
            {item.links && (
              <a
                href={item.links}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-blue-600 hover:underline"
              >
                Quick link <ExternalLink className="h-3.5 w-3.5" />
              </a>
            )}
          </div>

          {/* Optional image + motivation kept minimal */}
          {(item.imageUrl || item.motivation || item.notes) && (
            <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
              {item.imageUrl && (
                <div className="sm:col-span-1">
                  <img src={item.imageUrl} alt={item.name} className="h-32 w-full rounded-lg object-cover" />
                </div>
              )}
              <div className={item.imageUrl ? "sm:col-span-2" : "sm:col-span-3"}>
                {item.motivation && (
                  <p className="text-sm text-gray-700 italic">“{item.motivation}”</p>
                )}
                {item.notes && (
                  <p className="mt-2 text-sm text-gray-700">{item.notes}</p>
                )}
              </div>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Possibilities */}
      <div className="space-y-6">

        {/* Possibilities Table */}
        <Card className="border-0 shadow-xl">
          <CardContent className="p-0">
            <WishlistPossibilitiesTable 
              possibilities={item.possibilities || []} 
              wishlistItemName={item.name}
              onEditPossibility={(possibility) => {
                setEditingPossibility(possibility);
                setPossibilitySheetOpen(true);
              }}
              onEditSource={(source, possibilityId) => {
                setEditingSource({ source, possibilityId });
                setSourceSheetOpen(true);
              }}
              onAddSource={(possibilityId) => {
                setEditingSource(null);
                setAddingSourceToPossibilityId(possibilityId);
                setSourceSheetOpen(true);
              }}
            />
          </CardContent>
        </Card>

        {/* Possibility Sheet */}
        <Sheet open={possibilitySheetOpen} onOpenChange={setPossibilitySheetOpen}>
          <SheetContent className="w-[600px] sm:w-[540px]">
            <SheetHeader>
              <SheetTitle>
                {editingPossibility ? "Edit" : "Add"} Possibility
              </SheetTitle>
            </SheetHeader>
            <div className="mt-6">
              <WishlistPossibilityForm
                id={editingPossibility?.id}
                defaultValues={editingPossibility ? {
                  name: editingPossibility.name,
                  description: editingPossibility.description || "",
                  notes: editingPossibility.notes || "",
                } : undefined}
                onSubmit={editingPossibility ? handleEditPossibility : handleCreatePossibility}
                onDelete={editingPossibility ? handleDeletePossibility : undefined}
                disabled={createPossibilityMutation.isPending || editPossibilityMutation.isPending || deletePossibilityMutation.isPending}
              />
            </div>
          </SheetContent>
        </Sheet>

        {/* Source Sheet */}
        <Sheet open={sourceSheetOpen} onOpenChange={setSourceSheetOpen}>
          <SheetContent className="w-[500px] sm:w-[440px]">
            <SheetHeader>
              <SheetTitle>
                {editingSource ? "Edit" : "Add"} Source
              </SheetTitle>
            </SheetHeader>
            <div className="mt-6">
              <WishlistSourceForm
                id={editingSource?.source.id}
                defaultValues={editingSource ? {
                  name: editingSource.source.name,
                  url: editingSource.source.url || "",
                  price: editingSource.source.price?.toString() || "",
                  notes: editingSource.source.notes || "",
                } : undefined}
                onSubmit={(values) => {
                  if (editingSource) {
                    handleEditSource(values);
                  } else {
                    handleCreateSource(values);
                  }
                }}
                onDelete={editingSource ? handleDeleteSource : undefined}
                disabled={createSourceMutation.isPending || editSourceMutation.isPending || deleteSourceMutation.isPending}
              />
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
};