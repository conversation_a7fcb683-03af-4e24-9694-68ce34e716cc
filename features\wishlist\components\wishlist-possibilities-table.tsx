"use client";

import React from "react";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowUpDown, 
  ChevronDown, 
  ChevronUp, 
  Edit, 
  Trash, 
  MoreHorizontal,
  Plus 
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { motion, AnimatePresence } from "framer-motion";
import { WishlistSourcesTable, WishlistSource } from "./wishlist-sources-table";
import { useDeleteWishlistPossibility } from "@/features/wishlist/api/use-delete-wishlist-possibility";
import { MediaTablePreview } from "@/components/media-table-preview";

export type WishlistPossibility = {
  id: string;
  name: string;
  description?: string | null;
  notes?: string | null;
  wishlistItemId: string;
  sources: WishlistSource[];
  createdAt: Date;
  updatedAt: Date;
};

type Props = {
  possibilities: WishlistPossibility[];
  wishlistItemName: string;
  onEditPossibility?: (possibility: WishlistPossibility) => void;
  onEditSource?: (source: WishlistSource, possibilityId: string) => void;
  onAddSource?: (possibilityId: string) => void;
};

const ActionsCell = ({ possibility, onEditPossibility, onAddSource }: { possibility: WishlistPossibility; onEditPossibility?: (possibility: WishlistPossibility) => void; onAddSource?: (possibilityId: string) => void }) => {
  const deleteMutation = useDeleteWishlistPossibility(possibility.id);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onEditPossibility?.(possibility)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onAddSource?.(possibility.id)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Source
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => deleteMutation.mutate()}
          disabled={deleteMutation.isPending}
          className="text-red-600"
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const WishlistPossibilitiesTable = ({ possibilities, wishlistItemName, onEditPossibility, onEditSource, onAddSource }: Props) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = React.useState({});
  const [expanded, setExpanded] = React.useState({});

  const columns: ColumnDef<WishlistPossibility>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Possibility Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const name = row.getValue("name") as string;
        const hasSources = row.original.sources && row.original.sources.length > 0;
        
        return (
          <div className="flex items-center justify-between">
            <span className="font-medium">{name}</span>
            {hasSources && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => row.toggleExpanded()}
                className="text-xs hover:bg-none hover:text-indigo-600 text-indigo-600"
              >
                {row.getIsExpanded() ? (
                  <>
                    Hide Sources <ChevronUp className="ml-1 h-3 w-3" />
                  </>
                ) : (
                  <>
                    View Sources ({row.original.sources.length}) <ChevronDown className="ml-1 h-3 w-3" />
                  </>
                )}
              </Button>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.getValue("description") as string | null;
        
        if (!description) {
          return <span className="text-gray-500">-</span>;
        }
        
        return (
          <span className="text-sm text-gray-700 max-w-[300px] truncate">
            {description}
          </span>
        );
      },
    },
    {
      accessorKey: "sources",
      header: "Sources",
      cell: ({ row }) => {
        const sourcesCount = row.original.sources.length;
        
        if (sourcesCount === 0) {
          return <Badge variant="secondary" className="text-xs">No sources</Badge>;
        }
        
        return (
          <Badge variant="default" className="text-xs">
            {sourcesCount} source{sourcesCount > 1 ? "s" : ""}
          </Badge>
        );
      },
    },
    {
      id: "media",
      header: "Media",
      cell: ({ row }) => (
        <MediaTablePreview
          entityType="wishlist_possibility"
          entityId={row.original.id}
          category="images"
          compact={true}
        />
      ),
    },
    {
      accessorKey: "notes",
      header: "Notes",
      cell: ({ row }) => {
        const notes = row.getValue("notes") as string | null;
        
        if (!notes) {
          return <span className="text-gray-500">-</span>;
        }
        
        return (
          <span className="text-sm text-gray-600 max-w-[200px] truncate">
            {notes}
          </span>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => <ActionsCell possibility={row.original} onEditPossibility={onEditPossibility} onAddSource={onAddSource} />,
    },
  ];

  const table = useReactTable({
    data: possibilities,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onExpandedChange: setExpanded,
    getExpandedRowModel: getExpandedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      rowSelection,
      expanded,
    },
  });

  if (!possibilities.length) {
    return (
      <div className="bg-slate-50 p-6 text-center">
        <p className="text-gray-600 mb-2">No possibilities found for "{wishlistItemName}"</p>
        <p className="text-sm text-gray-500">Add some possibilities to compare options and sources.</p>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-indigo-50 to-cyan-50 p-4 border-l-4 border-l-indigo-400">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-1">
          Possibilities for "{wishlistItemName}"
        </h3>
        <p className="text-sm text-gray-600">
          {possibilities.length} option{possibilities.length > 1 ? "s" : ""} available
        </p>
      </div>

      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <React.Fragment key={row.id}>
                <TableRow
                  data-state={row.getIsSelected() && "selected"}
                  className={row.getIsExpanded() ? "border-b-0" : ""}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
                <AnimatePresence>
                  {row.getIsExpanded() && (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="p-0">
                        <motion.div
                          key={`sources-${row.id}`}
                          initial="collapsed"
                          animate="open"
                          exit="collapsed"
                          variants={{
                            open: { height: "auto", opacity: 1 },
                            collapsed: { height: 0, opacity: 0 },
                          }}
                          transition={{
                            duration: 0.2,
                            ease: [0.04, 0.62, 0.23, 0.98],
                          }}
                          className="overflow-hidden"
                        >
                          <WishlistSourcesTable 
                            sources={row.original.sources} 
                            onEditSource={(source) => onEditSource?.(source, row.original.id)}
                          />
                        </motion.div>
                      </TableCell>
                    </TableRow>
                  )}
                </AnimatePresence>
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};